/*! This file is auto-generated */
/*
 * Button mixin- creates a button effect with correct
 * highlights/shadows, based on a base color.
 */
/**
 * This function name uses British English to maintain backward compatibility, as developers
 * may use the function in their own admin CSS files. See #56811.
 */
body {
  background: #f1f1f1;
}

/* Links */
a {
  color: #0073aa;
}
a:hover, a:active, a:focus {
  color: rgb(0, 149.5, 221);
}

#post-body .misc-pub-post-status:before,
#post-body #visibility:before,
.curtime #timestamp:before,
#post-body .misc-pub-revisions:before,
span.wp-media-buttons-icon:before {
  color: currentColor;
}

.wp-core-ui .button-link {
  color: #0073aa;
}
.wp-core-ui .button-link:hover, .wp-core-ui .button-link:active, .wp-core-ui .button-link:focus {
  color: rgb(0, 149.5, 221);
}

.media-modal .delete-attachment,
.media-modal .trash-attachment,
.media-modal .untrash-attachment,
.wp-core-ui .button-link-delete {
  color: #a00;
}

.media-modal .delete-attachment:hover,
.media-modal .trash-attachment:hover,
.media-modal .untrash-attachment:hover,
.media-modal .delete-attachment:focus,
.media-modal .trash-attachment:focus,
.media-modal .untrash-attachment:focus,
.wp-core-ui .button-link-delete:hover,
.wp-core-ui .button-link-delete:focus {
  color: #dc3232;
}

/* Forms */
input[type=checkbox]:checked::before {
  content: url("data:image/svg+xml;utf8,%3Csvg%20xmlns%3D%27http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%27%20viewBox%3D%270%200%2020%2020%27%3E%3Cpath%20d%3D%27M14.83%204.89l1.34.94-5.81%208.38H9.02L5.78%209.67l1.34-1.25%202.57%202.4z%27%20fill%3D%27%23523f6d%27%2F%3E%3C%2Fsvg%3E");
}

input[type=radio]:checked::before {
  background: #523f6d;
}

.wp-core-ui input[type=reset]:hover,
.wp-core-ui input[type=reset]:active {
  color: rgb(0, 149.5, 221);
}

input[type=text]:focus,
input[type=password]:focus,
input[type=color]:focus,
input[type=date]:focus,
input[type=datetime]:focus,
input[type=datetime-local]:focus,
input[type=email]:focus,
input[type=month]:focus,
input[type=number]:focus,
input[type=search]:focus,
input[type=tel]:focus,
input[type=text]:focus,
input[type=time]:focus,
input[type=url]:focus,
input[type=week]:focus,
input[type=checkbox]:focus,
input[type=radio]:focus,
select:focus,
textarea:focus {
  border-color: #a3b745;
  box-shadow: 0 0 0 1px #a3b745;
}

/* Core UI */
.wp-core-ui .button {
  border-color: #7e8993;
  color: #32373c;
}
.wp-core-ui .button.hover,
.wp-core-ui .button:hover,
.wp-core-ui .button.focus,
.wp-core-ui .button:focus {
  border-color: rgb(112.7848101266, 124.2721518987, 134.7151898734);
  color: rgb(38.4090909091, 42.25, 46.0909090909);
}
.wp-core-ui .button.focus,
.wp-core-ui .button:focus {
  border-color: #7e8993;
  color: rgb(38.4090909091, 42.25, 46.0909090909);
  box-shadow: 0 0 0 1px #32373c;
}
.wp-core-ui .button:active {
  border-color: #7e8993;
  color: rgb(38.4090909091, 42.25, 46.0909090909);
  box-shadow: none;
}
.wp-core-ui .button.active,
.wp-core-ui .button.active:focus,
.wp-core-ui .button.active:hover {
  border-color: #a3b745;
  color: rgb(38.4090909091, 42.25, 46.0909090909);
  box-shadow: inset 0 2px 5px -3px #a3b745;
}
.wp-core-ui .button.active:focus {
  box-shadow: 0 0 0 1px #32373c;
}
.wp-core-ui .button,
.wp-core-ui .button-secondary {
  color: #a3b745;
  border-color: #a3b745;
}
.wp-core-ui .button.hover,
.wp-core-ui .button:hover,
.wp-core-ui .button-secondary:hover {
  border-color: rgb(130.0119047619, 145.9642857143, 55.0357142857);
  color: rgb(130.0119047619, 145.9642857143, 55.0357142857);
}
.wp-core-ui .button.focus,
.wp-core-ui .button:focus,
.wp-core-ui .button-secondary:focus {
  border-color: rgb(181.8928571429, 198.3214285714, 104.6785714286);
  color: rgb(97.0238095238, 108.9285714286, 41.0714285714);
  box-shadow: 0 0 0 1px rgb(181.8928571429, 198.3214285714, 104.6785714286);
}
.wp-core-ui .button-primary:hover {
  color: #fff;
}
.wp-core-ui .button-primary {
  background: #a3b745;
  border-color: #a3b745;
  color: #fff;
}
.wp-core-ui .button-primary:hover, .wp-core-ui .button-primary:focus {
  background: rgb(169.2845238095, 188.5464285714, 78.7535714286);
  border-color: rgb(153.1035714286, 171.8892857143, 64.8107142857);
  color: #fff;
}
.wp-core-ui .button-primary:focus {
  box-shadow: 0 0 0 1px #fff, 0 0 0 3px #a3b745;
}
.wp-core-ui .button-primary:active {
  background: rgb(146.*********, 164.**********, 62.**********);
  border-color: rgb(146.*********, 164.**********, 62.**********);
  color: #fff;
}
.wp-core-ui .button-primary.active, .wp-core-ui .button-primary.active:focus, .wp-core-ui .button-primary.active:hover {
  background: #a3b745;
  color: #fff;
  border-color: rgb(113.5178571429, 127.4464285714, 48.0535714286);
  box-shadow: inset 0 2px 5px -3px hsl(70.5263157895, 45.2380952381%, -0.5882352941%);
}
.wp-core-ui .button-group > .button.active {
  border-color: #a3b745;
}
.wp-core-ui .wp-ui-primary {
  color: #fff;
  background-color: #523f6d;
}
.wp-core-ui .wp-ui-text-primary {
  color: #523f6d;
}
.wp-core-ui .wp-ui-highlight {
  color: #fff;
  background-color: #a3b745;
}
.wp-core-ui .wp-ui-text-highlight {
  color: #a3b745;
}
.wp-core-ui .wp-ui-notification {
  color: #fff;
  background-color: #d46f15;
}
.wp-core-ui .wp-ui-text-notification {
  color: #d46f15;
}
.wp-core-ui .wp-ui-text-icon {
  color: #ece6f6;
}

/* List tables */
.wrap .page-title-action,
.wrap .page-title-action:active {
  border: 1px solid #a3b745;
  color: #a3b745;
}

.wrap .page-title-action:hover {
  color: rgb(130.0119047619, 145.9642857143, 55.0357142857);
  border-color: rgb(130.0119047619, 145.9642857143, 55.0357142857);
}

.wrap .page-title-action:focus {
  border-color: rgb(181.8928571429, 198.3214285714, 104.6785714286);
  color: rgb(97.0238095238, 108.9285714286, 41.0714285714);
  box-shadow: 0 0 0 1px rgb(181.8928571429, 198.3214285714, 104.6785714286);
}

.view-switch a.current:before {
  color: #523f6d;
}

.view-switch a:hover:before {
  color: #d46f15;
}

/* Admin Menu */
#adminmenuback,
#adminmenuwrap,
#adminmenu {
  background: #523f6d;
}

#adminmenu a {
  color: #fff;
}

#adminmenu div.wp-menu-image:before {
  color: #ece6f6;
}

#adminmenu a:hover,
#adminmenu li.menu-top:hover,
#adminmenu li.opensub > a.menu-top,
#adminmenu li > a.menu-top:focus {
  color: #fff;
  background-color: #a3b745;
}

#adminmenu li.menu-top:hover div.wp-menu-image:before,
#adminmenu li.opensub > a.menu-top div.wp-menu-image:before {
  color: #fff;
}

/* Active tabs use a bottom border color that matches the page background color. */
.about-wrap .nav-tab-active,
.nav-tab-active,
.nav-tab-active:hover {
  background-color: #f1f1f1;
  border-bottom-color: #f1f1f1;
}

/* Admin Menu: submenu */
#adminmenu .wp-submenu,
#adminmenu .wp-has-current-submenu .wp-submenu,
#adminmenu .wp-has-current-submenu.opensub .wp-submenu,
#adminmenu a.wp-has-current-submenu:focus + .wp-submenu {
  background: rgb(64.9802325581, 49.9238372093, 86.3761627907);
}

#adminmenu li.wp-has-submenu.wp-not-current-submenu.opensub:hover:after,
#adminmenu li.wp-has-submenu.wp-not-current-submenu:focus-within:after {
  border-left-color: rgb(64.9802325581, 49.9238372093, 86.3761627907);
}

#adminmenu .wp-submenu .wp-submenu-head {
  color: rgb(203.1, 197.4, 211.2);
}

#adminmenu .wp-submenu a,
#adminmenu .wp-has-current-submenu .wp-submenu a,
#adminmenu a.wp-has-current-submenu:focus + .wp-submenu a,
#adminmenu .wp-has-current-submenu.opensub .wp-submenu a {
  color: rgb(203.1, 197.4, 211.2);
}
#adminmenu .wp-submenu a:focus, #adminmenu .wp-submenu a:hover,
#adminmenu .wp-has-current-submenu .wp-submenu a:focus,
#adminmenu .wp-has-current-submenu .wp-submenu a:hover,
#adminmenu a.wp-has-current-submenu:focus + .wp-submenu a:focus,
#adminmenu a.wp-has-current-submenu:focus + .wp-submenu a:hover,
#adminmenu .wp-has-current-submenu.opensub .wp-submenu a:focus,
#adminmenu .wp-has-current-submenu.opensub .wp-submenu a:hover {
  color: #a3b745;
}

/* Admin Menu: current */
#adminmenu .wp-submenu li.current a,
#adminmenu a.wp-has-current-submenu:focus + .wp-submenu li.current a,
#adminmenu .wp-has-current-submenu.opensub .wp-submenu li.current a {
  color: #fff;
}
#adminmenu .wp-submenu li.current a:hover, #adminmenu .wp-submenu li.current a:focus,
#adminmenu a.wp-has-current-submenu:focus + .wp-submenu li.current a:hover,
#adminmenu a.wp-has-current-submenu:focus + .wp-submenu li.current a:focus,
#adminmenu .wp-has-current-submenu.opensub .wp-submenu li.current a:hover,
#adminmenu .wp-has-current-submenu.opensub .wp-submenu li.current a:focus {
  color: #a3b745;
}

ul#adminmenu a.wp-has-current-submenu:after,
ul#adminmenu > li.current > a.current:after {
  border-left-color: #f1f1f1;
}

#adminmenu li.current a.menu-top,
#adminmenu li.wp-has-current-submenu a.wp-has-current-submenu,
#adminmenu li.wp-has-current-submenu .wp-submenu .wp-submenu-head,
.folded #adminmenu li.current.menu-top {
  color: #fff;
  background: #a3b745;
}

#adminmenu li.wp-has-current-submenu div.wp-menu-image:before,
#adminmenu a.current:hover div.wp-menu-image:before,
#adminmenu li.current div.wp-menu-image:before,
#adminmenu li.wp-has-current-submenu a:focus div.wp-menu-image:before,
#adminmenu li.wp-has-current-submenu.opensub div.wp-menu-image:before,
#adminmenu li:hover div.wp-menu-image:before,
#adminmenu li a:focus div.wp-menu-image:before,
#adminmenu li.opensub div.wp-menu-image:before {
  color: #fff;
}

/* Admin Menu: bubble */
#adminmenu .menu-counter,
#adminmenu .awaiting-mod,
#adminmenu .update-plugins {
  color: #fff;
  background: #d46f15;
}

#adminmenu li.current a .awaiting-mod,
#adminmenu li a.wp-has-current-submenu .update-plugins,
#adminmenu li:hover a .awaiting-mod,
#adminmenu li.menu-top:hover > a .update-plugins {
  color: #fff;
  background: rgb(64.9802325581, 49.9238372093, 86.3761627907);
}

/* Admin Menu: collapse button */
#collapse-button {
  color: #ece6f6;
}

#collapse-button:hover,
#collapse-button:focus {
  color: #a3b745;
}

/* Admin Bar */
#wpadminbar {
  color: #fff;
  background: #523f6d;
}

#wpadminbar .ab-item,
#wpadminbar a.ab-item,
#wpadminbar > #wp-toolbar span.ab-label,
#wpadminbar > #wp-toolbar span.noticon {
  color: #fff;
}

#wpadminbar .ab-icon,
#wpadminbar .ab-icon:before,
#wpadminbar .ab-item:before,
#wpadminbar .ab-item:after {
  color: #ece6f6;
}

#wpadminbar:not(.mobile) .ab-top-menu > li:hover > .ab-item,
#wpadminbar:not(.mobile) .ab-top-menu > li > .ab-item:focus,
#wpadminbar.nojq .quicklinks .ab-top-menu > li > .ab-item:focus,
#wpadminbar.nojs .ab-top-menu > li.menupop:hover > .ab-item,
#wpadminbar .ab-top-menu > li.menupop.hover > .ab-item {
  color: #a3b745;
  background: rgb(64.9802325581, 49.9238372093, 86.3761627907);
}

#wpadminbar:not(.mobile) > #wp-toolbar li:hover span.ab-label,
#wpadminbar:not(.mobile) > #wp-toolbar li.hover span.ab-label,
#wpadminbar:not(.mobile) > #wp-toolbar a:focus span.ab-label {
  color: #a3b745;
}

#wpadminbar:not(.mobile) li:hover .ab-icon:before,
#wpadminbar:not(.mobile) li:hover .ab-item:before,
#wpadminbar:not(.mobile) li:hover .ab-item:after,
#wpadminbar:not(.mobile) li:hover #adminbarsearch:before {
  color: #a3b745;
}

/* Admin Bar: submenu */
#wpadminbar .menupop .ab-sub-wrapper {
  background: rgb(64.9802325581, 49.9238372093, 86.3761627907);
}

#wpadminbar .quicklinks .menupop ul.ab-sub-secondary,
#wpadminbar .quicklinks .menupop ul.ab-sub-secondary .ab-submenu {
  background: rgb(100.2840283114, 83.3456627907, 124.3543372093);
}

#wpadminbar .ab-submenu .ab-item,
#wpadminbar .quicklinks .menupop ul li a,
#wpadminbar .quicklinks .menupop.hover ul li a,
#wpadminbar.nojs .quicklinks .menupop:hover ul li a {
  color: rgb(203.1, 197.4, 211.2);
}

#wpadminbar .quicklinks li .blavatar,
#wpadminbar .menupop .menupop > .ab-item:before {
  color: #ece6f6;
}

#wpadminbar .quicklinks .menupop ul li a:hover,
#wpadminbar .quicklinks .menupop ul li a:focus,
#wpadminbar .quicklinks .menupop ul li a:hover strong,
#wpadminbar .quicklinks .menupop ul li a:focus strong,
#wpadminbar .quicklinks .ab-sub-wrapper .menupop.hover > a,
#wpadminbar .quicklinks .menupop.hover ul li a:hover,
#wpadminbar .quicklinks .menupop.hover ul li a:focus,
#wpadminbar.nojs .quicklinks .menupop:hover ul li a:hover,
#wpadminbar.nojs .quicklinks .menupop:hover ul li a:focus,
#wpadminbar li:hover .ab-icon:before,
#wpadminbar li:hover .ab-item:before,
#wpadminbar li a:focus .ab-icon:before,
#wpadminbar li .ab-item:focus:before,
#wpadminbar li .ab-item:focus .ab-icon:before,
#wpadminbar li.hover .ab-icon:before,
#wpadminbar li.hover .ab-item:before,
#wpadminbar li:hover #adminbarsearch:before,
#wpadminbar li #adminbarsearch.adminbar-focused:before {
  color: #a3b745;
}

#wpadminbar .quicklinks li a:hover .blavatar,
#wpadminbar .quicklinks li a:focus .blavatar,
#wpadminbar .quicklinks .ab-sub-wrapper .menupop.hover > a .blavatar,
#wpadminbar .menupop .menupop > .ab-item:hover:before,
#wpadminbar.mobile .quicklinks .ab-icon:before,
#wpadminbar.mobile .quicklinks .ab-item:before {
  color: #a3b745;
}

#wpadminbar.mobile .quicklinks .hover .ab-icon:before,
#wpadminbar.mobile .quicklinks .hover .ab-item:before {
  color: #ece6f6;
}

/* Admin Bar: search */
#wpadminbar #adminbarsearch:before {
  color: #ece6f6;
}

#wpadminbar > #wp-toolbar > #wp-admin-bar-top-secondary > #wp-admin-bar-search #adminbarsearch input.adminbar-input:focus {
  color: #fff;
  background: rgb(99.**********, 76.**********, 131.**********);
}

/* Admin Bar: recovery mode */
#wpadminbar #wp-admin-bar-recovery-mode {
  color: #fff;
  background-color: #d46f15;
}

#wpadminbar #wp-admin-bar-recovery-mode .ab-item,
#wpadminbar #wp-admin-bar-recovery-mode a.ab-item {
  color: #fff;
}

#wpadminbar .ab-top-menu > #wp-admin-bar-recovery-mode.hover > .ab-item,
#wpadminbar.nojq .quicklinks .ab-top-menu > #wp-admin-bar-recovery-mode > .ab-item:focus,
#wpadminbar:not(.mobile) .ab-top-menu > #wp-admin-bar-recovery-mode:hover > .ab-item,
#wpadminbar:not(.mobile) .ab-top-menu > #wp-admin-bar-recovery-mode > .ab-item:focus {
  color: #fff;
  background-color: rgb(190.8, 99.9, 18.9);
}

/* Admin Bar: my account */
#wpadminbar .quicklinks li#wp-admin-bar-my-account.with-avatar > a img {
  border-color: rgb(99.**********, 76.**********, 131.**********);
  background-color: rgb(99.**********, 76.**********, 131.**********);
}

#wpadminbar #wp-admin-bar-user-info .display-name {
  color: #fff;
}

#wpadminbar #wp-admin-bar-user-info a:hover .display-name {
  color: #a3b745;
}

#wpadminbar #wp-admin-bar-user-info .username {
  color: rgb(203.1, 197.4, 211.2);
}

/* Pointers */
.wp-pointer .wp-pointer-content h3 {
  background-color: #a3b745;
  border-color: rgb(146.*********, 164.**********, 62.**********);
}

.wp-pointer .wp-pointer-content h3:before {
  color: #a3b745;
}

.wp-pointer.wp-pointer-top .wp-pointer-arrow,
.wp-pointer.wp-pointer-top .wp-pointer-arrow-inner,
.wp-pointer.wp-pointer-undefined .wp-pointer-arrow,
.wp-pointer.wp-pointer-undefined .wp-pointer-arrow-inner {
  border-bottom-color: #a3b745;
}

/* Media */
.media-item .bar,
.media-progress-bar div {
  background-color: #a3b745;
}

.details.attachment {
  box-shadow: inset 0 0 0 3px #fff, inset 0 0 0 7px #a3b745;
}

.attachment.details .check {
  background-color: #a3b745;
  box-shadow: 0 0 0 1px #fff, 0 0 0 2px #a3b745;
}

.media-selection .attachment.selection.details .thumbnail {
  box-shadow: 0 0 0 1px #fff, 0 0 0 3px #a3b745;
}

/* Themes */
.theme-browser .theme.active .theme-name,
.theme-browser .theme.add-new-theme a:hover:after,
.theme-browser .theme.add-new-theme a:focus:after {
  background: #a3b745;
}

.theme-browser .theme.add-new-theme a:hover span:after,
.theme-browser .theme.add-new-theme a:focus span:after {
  color: #a3b745;
}

.theme-section.current,
.theme-filter.current {
  border-bottom-color: #523f6d;
}

body.more-filters-opened .more-filters {
  color: #fff;
  background-color: #523f6d;
}

body.more-filters-opened .more-filters:before {
  color: #fff;
}

body.more-filters-opened .more-filters:hover,
body.more-filters-opened .more-filters:focus {
  background-color: #a3b745;
  color: #fff;
}

body.more-filters-opened .more-filters:hover:before,
body.more-filters-opened .more-filters:focus:before {
  color: #fff;
}

/* Widgets */
.widgets-chooser li.widgets-chooser-selected {
  background-color: #a3b745;
  color: #fff;
}

.widgets-chooser li.widgets-chooser-selected:before,
.widgets-chooser li.widgets-chooser-selected:focus:before {
  color: #fff;
}

/* Nav Menus */
.nav-menus-php .item-edit:focus:before {
  box-shadow: 0 0 0 1px rgb(181.8928571429, 198.3214285714, 104.6785714286), 0 0 2px 1px #a3b745;
}

/* Responsive Component */
div#wp-responsive-toggle a:before {
  color: #ece6f6;
}

.wp-responsive-open div#wp-responsive-toggle a {
  border-color: transparent;
  background: #a3b745;
}

.wp-responsive-open #wpadminbar #wp-admin-bar-menu-toggle a {
  background: rgb(64.9802325581, 49.9238372093, 86.3761627907);
}

.wp-responsive-open #wpadminbar #wp-admin-bar-menu-toggle .ab-icon:before {
  color: #ece6f6;
}

/* TinyMCE */
.mce-container.mce-menu .mce-menu-item:hover,
.mce-container.mce-menu .mce-menu-item.mce-selected,
.mce-container.mce-menu .mce-menu-item:focus,
.mce-container.mce-menu .mce-menu-item-normal.mce-active,
.mce-container.mce-menu .mce-menu-item-preview.mce-active {
  background: #a3b745;
}

/* Customizer */
.wp-core-ui #customize-controls .control-section:hover > .accordion-section-title,
.wp-core-ui #customize-controls .control-section .accordion-section-title:hover,
.wp-core-ui #customize-controls .control-section.open .accordion-section-title,
.wp-core-ui #customize-controls .control-section .accordion-section-title:focus {
  color: #0073aa;
  border-right-color: #a3b745;
}
.wp-core-ui .customize-controls-close:focus,
.wp-core-ui .customize-controls-close:hover,
.wp-core-ui .customize-controls-preview-toggle:focus,
.wp-core-ui .customize-controls-preview-toggle:hover {
  color: #0073aa;
  border-top-color: #a3b745;
}
.wp-core-ui .customize-panel-back:hover,
.wp-core-ui .customize-panel-back:focus,
.wp-core-ui .customize-section-back:hover,
.wp-core-ui .customize-section-back:focus {
  color: #0073aa;
  border-right-color: #a3b745;
}
.wp-core-ui .customize-screen-options-toggle:hover,
.wp-core-ui .customize-screen-options-toggle:active,
.wp-core-ui .customize-screen-options-toggle:focus,
.wp-core-ui .active-menu-screen-options .customize-screen-options-toggle,
.wp-core-ui #customize-controls .customize-info.open.active-menu-screen-options .customize-help-toggle:hover,
.wp-core-ui #customize-controls .customize-info.open.active-menu-screen-options .customize-help-toggle:active,
.wp-core-ui #customize-controls .customize-info.open.active-menu-screen-options .customize-help-toggle:focus {
  color: #0073aa;
}
.wp-core-ui .customize-screen-options-toggle:focus:before,
.wp-core-ui #customize-controls .customize-info .customize-help-toggle:focus:before, .wp-core-ui.wp-customizer button:focus .toggle-indicator:before,
.wp-core-ui .menu-item-bar .item-delete:focus:before,
.wp-core-ui #available-menu-items .item-add:focus:before,
.wp-core-ui #customize-save-button-wrapper .save:focus,
.wp-core-ui #publish-settings:focus {
  box-shadow: 0 0 0 1px rgb(181.8928571429, 198.3214285714, 104.6785714286), 0 0 2px 1px #a3b745;
}
.wp-core-ui #customize-controls .customize-info.open .customize-help-toggle,
.wp-core-ui #customize-controls .customize-info .customize-help-toggle:focus,
.wp-core-ui #customize-controls .customize-info .customize-help-toggle:hover {
  color: #0073aa;
}
.wp-core-ui .control-panel-themes .customize-themes-section-title:focus,
.wp-core-ui .control-panel-themes .customize-themes-section-title:hover {
  border-right-color: #a3b745;
  color: #0073aa;
}
.wp-core-ui .control-panel-themes .theme-section .customize-themes-section-title.selected:after {
  background: #a3b745;
}
.wp-core-ui .control-panel-themes .customize-themes-section-title.selected {
  color: #0073aa;
}
.wp-core-ui #customize-theme-controls .control-section:hover > .accordion-section-title:after,
.wp-core-ui #customize-theme-controls .control-section .accordion-section-title:hover:after,
.wp-core-ui #customize-theme-controls .control-section.open .accordion-section-title:after,
.wp-core-ui #customize-theme-controls .control-section .accordion-section-title:focus:after,
.wp-core-ui #customize-outer-theme-controls .control-section:hover > .accordion-section-title:after,
.wp-core-ui #customize-outer-theme-controls .control-section .accordion-section-title:hover:after,
.wp-core-ui #customize-outer-theme-controls .control-section.open .accordion-section-title:after,
.wp-core-ui #customize-outer-theme-controls .control-section .accordion-section-title:focus:after {
  color: #0073aa;
}
.wp-core-ui .customize-control .attachment-media-view .button-add-media:focus {
  background-color: #fbfbfc;
  border-color: #a3b745;
  border-style: solid;
  box-shadow: 0 0 0 1px #a3b745;
  outline: 2px solid transparent;
}
.wp-core-ui .wp-full-overlay-footer .devices button:focus,
.wp-core-ui .wp-full-overlay-footer .devices button.active:hover {
  border-bottom-color: #a3b745;
}
.wp-core-ui .wp-full-overlay-footer .devices button:hover:before,
.wp-core-ui .wp-full-overlay-footer .devices button:focus:before {
  color: #a3b745;
}
.wp-core-ui .wp-full-overlay .collapse-sidebar:hover,
.wp-core-ui .wp-full-overlay .collapse-sidebar:focus {
  color: #a3b745;
}
.wp-core-ui .wp-full-overlay .collapse-sidebar:hover .collapse-sidebar-arrow,
.wp-core-ui .wp-full-overlay .collapse-sidebar:focus .collapse-sidebar-arrow {
  box-shadow: 0 0 0 1px rgb(181.8928571429, 198.3214285714, 104.6785714286), 0 0 2px 1px #a3b745;
}
.wp-core-ui.wp-customizer .theme-overlay .theme-header .close:focus, .wp-core-ui.wp-customizer .theme-overlay .theme-header .close:hover, .wp-core-ui.wp-customizer .theme-overlay .theme-header .right:focus, .wp-core-ui.wp-customizer .theme-overlay .theme-header .right:hover, .wp-core-ui.wp-customizer .theme-overlay .theme-header .left:focus, .wp-core-ui.wp-customizer .theme-overlay .theme-header .left:hover {
  border-bottom-color: #a3b745;
  color: #0073aa;
}